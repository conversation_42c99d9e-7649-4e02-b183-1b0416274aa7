package com.example.message_divert

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterActivity() {
    private val SMS_RECEIVER_CHANNEL = "sms_receiver"
    private val SMS_MANAGER_CHANNEL = "sms_manager"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // SMS Receiver channel
        val smsReceiverChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, SMS_RECEIVER_CHANNEL)
        SmsReceiver.methodChannel = smsReceiverChannel

        // SMS Manager channel
        val smsManagerChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, SMS_MANAGER_CHANNEL)
        smsManagerChannel.setMethodCallHandler(SmsManagerHandler(this))
    }
}
