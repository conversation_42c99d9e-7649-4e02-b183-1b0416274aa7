import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';

class SmsDebugPage extends StatefulWidget {
  const SmsDebugPage({super.key});

  @override
  State<SmsDebugPage> createState() => _SmsDebugPageState();
}

class _SmsDebugPageState extends State<SmsDebugPage> {
  static const MethodChannel _smsChannel = MethodChannel('sms_manager');
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _messageController = TextEditingController();
  String _debugLog = '';

  @override
  void initState() {
    super.initState();
    _checkPermissions();
  }

  void _addToLog(String message) {
    setState(() {
      _debugLog += '${DateTime.now().toString().substring(11, 19)}: $message\n';
    });
    print('SMS Debug: $message');
  }

  Future<void> _checkPermissions() async {
    _addToLog('Checking SMS permissions...');
    
    // Check via platform channel
    try {
      final hasNativePermission = await _smsChannel.invokeMethod('hasPermission');
      _addToLog('Native SMS permission: $hasNativePermission');
    } catch (e) {
      _addToLog('Error checking native permission: $e');
    }

    // Check via permission_handler
    final status = await Permission.sms.status;
    _addToLog('Permission handler SMS status: $status');

    if (!status.isGranted) {
      _addToLog('Requesting SMS permission...');
      final result = await Permission.sms.request();
      _addToLog('Permission request result: $result');
    }
  }

  Future<void> _testSms() async {
    if (_phoneController.text.isEmpty || _messageController.text.isEmpty) {
      _addToLog('ERROR: Phone number and message are required');
      return;
    }

    _addToLog('Testing SMS send...');
    _addToLog('Phone: ${_phoneController.text}');
    _addToLog('Message: ${_messageController.text}');

    try {
      // Check permission first
      final hasPermission = await _smsChannel.invokeMethod('hasPermission');
      _addToLog('Permission check result: $hasPermission');

      if (hasPermission != true) {
        _addToLog('ERROR: SMS permission not granted');
        return;
      }

      // Send SMS
      final result = await _smsChannel.invokeMethod('sendSms', {
        'phoneNumber': _phoneController.text,
        'message': _messageController.text,
      });

      _addToLog('SMS send result: $result');
      
      if (result == true) {
        _addToLog('SUCCESS: SMS sent successfully!');
      } else {
        _addToLog('ERROR: SMS send failed');
      }

    } catch (e) {
      _addToLog('ERROR: Exception during SMS send: $e');
    }
  }

  void _clearLog() {
    setState(() {
      _debugLog = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SMS Debug Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            TextField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone Number',
                hintText: '09123456789',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _messageController,
              decoration: const InputDecoration(
                labelText: 'Message',
                hintText: 'Test message',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _testSms,
                    child: const Text('Send Test SMS'),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _checkPermissions,
                  child: const Text('Check Permissions'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _clearLog,
                  child: const Text('Clear Log'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Debug Log:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _debugLog.isEmpty ? 'No logs yet...' : _debugLog,
                    style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
