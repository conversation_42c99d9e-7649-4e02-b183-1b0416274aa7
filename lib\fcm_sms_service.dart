import 'dart:convert';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'crypto_utils.dart';

class FcmSmsService {
  static final FcmSmsService _instance = FcmSmsService._internal();
  factory FcmSmsService() => _instance;
  FcmSmsService._internal();

  static const MethodChannel _smsChannel = MethodChannel('sms_manager');
  
  // Callback for UI notifications
  static Function(String message, bool isError)? onStatusUpdate;

  /// Initialize FCM message handlers
  void initialize() {
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // Handle background messages when app is opened from notification
    FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessage);
  }

  /// Handle FCM messages when app is in foreground
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    print('Received foreground FCM message: ${message.messageId}');
    await _processFcmMessage(message);
  }

  /// Handle FCM messages when app is opened from background
  Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    print('Received background FCM message: ${message.messageId}');
    await _processFcmMessage(message);
  }

  /// Process FCM message and send SMS if conditions are met
  Future<void> _processFcmMessage(RemoteMessage message) async {
    try {
      final data = message.data;
      
      // Log received data for debugging
      print('FCM Data: $data');
      
      // Check if this is a special SMS notification
      if (!_isSpecialSmsNotification(data)) {
        print(message.data);
        print('Not a special SMS notification, ignoring');
        return;
      }
      // Extract required fields
      final phoneNumber = data['phoneNumber'] ?? '';
      final smsMessage = data['message'] ?? '';
      final timestamp = data['timestamp'] ?? '';
      final signature = data['signature'] ?? '';

      // Validate required fields
      if (phoneNumber.isEmpty || smsMessage.isEmpty || timestamp.isEmpty || signature.isEmpty) {
        print("phoneNumber is $phoneNumber");
        print("smsMessage is $smsMessage");
        print("timestamp is $timestamp");
        print("signature is $signature");
        _notifyStatus('Invalid FCM payload: missing required fields', true);
        return;
      }

      // Validate phone number format
      if (!CryptoUtils.isValidIranianPhoneNumber(phoneNumber)) {
        _notifyStatus('Invalid Iranian phone number format: $phoneNumber', true);
        return;
      }

      // Verify signature
      print('Verifying signature...');
      print('Phone: $phoneNumber');
      print('Message: $smsMessage');
      print('Timestamp: $timestamp');
      print('Received signature: $signature');

      // Generate expected signature for debugging
      final expectedSignature = CryptoUtils.generateSignature(
        phoneNumber: phoneNumber,
        message: smsMessage,
        timestamp: timestamp,
      );
      print('Expected signature: $expectedSignature');

      if (!CryptoUtils.verifySignature(
        phoneNumber: phoneNumber,
        message: smsMessage,
        timestamp: timestamp,
        signature: signature,
      )) {
        _notifyStatus('Invalid signature verification failed. Expected: $expectedSignature, Got: $signature', true);
        return;
      }

      print('Signature verification passed!');

      // Check timestamp to prevent replay attacks (optional - within 5 minutes)
      if (!_isTimestampValid(timestamp)) {
        _notifyStatus('Timestamp validation failed - message too old', true);
        return;
      }

      // All validations passed, send SMS
      await _sendSms(phoneNumber, smsMessage);
      
    } catch (e) {
      print('Error processing FCM message: $e');
      _notifyStatus('Error processing FCM message: $e', true);
    }
  }

  /// Check if FCM data contains special SMS notification
  bool _isSpecialSmsNotification(Map<String, dynamic> data) {
    return data['type'] == 'special_sms';
  }

  /// Validate timestamp (within 5 minutes)
  bool _isTimestampValid(String timestampStr) {
    try {
      final timestamp = int.parse(timestampStr);
      final messageTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
      final now = DateTime.now();
      final difference = now.difference(messageTime).inMinutes;
      
      // Allow messages within 5 minutes
      return difference.abs() <= 5;
    } catch (e) {
      print('Error validating timestamp: $e');
      return false;
    }
  }

  /// Send SMS using platform channel
  Future<void> _sendSms(String phoneNumber, String message) async {
    try {
      // Check SMS permission
      final hasPermission = await _checkSmsPermission();
      if (!hasPermission) {
        _notifyStatus('SMS permission denied', true);
        return;
      }

      // Send SMS via platform channel
      final result = await _smsChannel.invokeMethod('sendSms', {
        'phoneNumber': phoneNumber,
        'message': message,
      });

      if (result == true) {
        _notifyStatus('SMS sent successfully to $phoneNumber', false);
        // Log successful SMS send
        await _logSmsActivity(phoneNumber, message, true);
      } else {
        _notifyStatus('Failed to send SMS: Unknown error', true);
        // Log failed SMS send
        await _logSmsActivity(phoneNumber, message, false);
      }

    } catch (e) {
      print('Error sending SMS: $e');
      _notifyStatus('Failed to send SMS: $e', true);

      // Log failed SMS send
      await _logSmsActivity(phoneNumber, message, false);
    }
  }

  /// Check and request SMS permission
  Future<bool> _checkSmsPermission() async {
    try {
      // First check via platform channel
      final hasNativePermission = await _smsChannel.invokeMethod('hasPermission');
      if (hasNativePermission == true) {
        return true;
      }

      // If not granted, request via permission_handler
      final status = await Permission.sms.status;
      if (status.isGranted) {
        return true;
      }

      // Request permission
      final result = await Permission.sms.request();
      return result.isGranted;
    } catch (e) {
      print('Error checking SMS permission: $e');
      return false;
    }
  }

  /// Log SMS activity to SharedPreferences
  Future<void> _logSmsActivity(String phoneNumber, String message, bool success) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final logs = prefs.getStringList('sms_logs') ?? [];
      
      final logEntry = jsonEncode({
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'phoneNumber': phoneNumber,
        'message': message,
        'success': success,
      });
      
      logs.add(logEntry);
      
      // Keep only last 100 logs
      if (logs.length > 100) {
        logs.removeRange(0, logs.length - 100);
      }
      
      await prefs.setStringList('sms_logs', logs);
    } catch (e) {
      print('Error logging SMS activity: $e');
    }
  }

  /// Notify status to UI
  void _notifyStatus(String message, bool isError) {
    print('Status: $message');
    if (onStatusUpdate != null) {
      onStatusUpdate!(message, isError);
    }
  }

  /// Get SMS logs for debugging
  Future<List<Map<String, dynamic>>> getSmsLogs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final logs = prefs.getStringList('sms_logs') ?? [];
      
      return logs.map((log) => jsonDecode(log) as Map<String, dynamic>).toList();
    } catch (e) {
      print('Error getting SMS logs: $e');
      return [];
    }
  }
}

/// Background message handler for FCM
/// This function must be top-level and annotated with @pragma('vm:entry-point')
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  print('Handling background FCM message: ${message.messageId}');
  print('FCM background data: ${message.data}');

  // Initialize Firebase if not already initialized
  try {
    await Firebase.initializeApp();
  } catch (e) {
    print('Firebase already initialized or error: $e');
  }

  // Process the message
  final fcmService = FcmSmsService();
  await fcmService._processFcmMessage(message);
}
