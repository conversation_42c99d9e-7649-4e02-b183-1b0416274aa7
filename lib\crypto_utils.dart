import 'dart:convert';
import 'package:crypto/crypto.dart';

class CryptoUtils {
  static const String _secretKey = "superSecretKey123";
  
  /// Verifies HMAC-SHA256 signature for FCM payload
  /// 
  /// Signature calculation: HMAC-SHA256(secret, phoneNumber+message+timestamp)
  /// 
  /// Returns true if signature is valid, false otherwise
  static bool verifySignature({
    required String phoneNumber,
    required String message,
    required String timestamp,
    required String signature,
  }) {
    try {
      // Create the data string to sign: phoneNumber + message + timestamp
      final dataToSign = phoneNumber + message + timestamp;
      
      // Calculate HMAC-SHA256
      final key = utf8.encode(_secretKey);
      final bytes = utf8.encode(dataToSign);
      final hmacSha256 = Hmac(sha256, key);
      final digest = hmacSha256.convert(bytes);
      
      // Convert to hex string
      final calculatedSignature = digest.toString();
      
      // Compare signatures (case-insensitive)
      return calculatedSignature.toLowerCase() == signature.toLowerCase();
    } catch (e) {
      print('Error verifying signature: $e');
      return false;
    }
  }
  
  /// Generates HMAC-SHA256 signature for testing purposes
  /// 
  /// This method can be used to generate valid signatures for testing
  static String generateSignature({
    required String phoneNumber,
    required String message,
    required String timestamp,
  }) {
    final dataToSign = phoneNumber + message + timestamp;
    final key = utf8.encode(_secretKey);
    final bytes = utf8.encode(dataToSign);
    final hmacSha256 = Hmac(sha256, key);
    final digest = hmacSha256.convert(bytes);
    return digest.toString();
  }
  
  /// Validates Iranian phone number format (09xxxxxxxxx)
  static bool isValidIranianPhoneNumber(String phoneNumber) {
    // Remove any non-digit characters
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    // Check if it's a valid Iranian mobile number (11 digits starting with 09)
    return cleanNumber.length == 11 && cleanNumber.startsWith('09');
  }
}
