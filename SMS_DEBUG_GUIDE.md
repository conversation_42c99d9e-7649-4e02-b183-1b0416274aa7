# SMS Debugging Guide

## Issues Found and Fixed

### 1. FCM Service Not Processing Data Payloads
**Problem**: The `MyFirebaseMessagingService` was only showing notifications but not processing data payloads for SMS sending.

**Fix**: Updated `MyFirebaseMessagingService.kt` to:
- Check for special SMS notification data
- Process SMS requests directly in the service
- Added comprehensive logging

### 2. Missing FCM Service Initialization
**Problem**: The `FcmSmsService` was not being properly initialized in the main app.

**Fix**: Added `_initializeFcmSmsService()` call in `initState()` of main app.

### 3. Background FCM Handler Issues
**Problem**: Background FCM handler wasn't properly initialized with Firebase.

**Fix**: Added proper Firebase initialization in background handler.

## How to Debug SMS Issues

### Step 1: Check Permissions
```bash
# Check if SMS permissions are granted
adb shell dumpsys package com.example.message_divert | grep -A 1 "android.permission.SEND_SMS"
```

### Step 2: Monitor Logs
```bash
# Monitor all app logs
adb logcat | grep -E "(FCMService|SmsManager|SMS|message_divert)"

# Monitor specific tags
adb logcat -s FCMService:D SmsManager:D MainActivity:D
```

### Step 3: Test SMS Functionality
1. Use the debug test page (`debug_sms_test.dart`)
2. Add it to your main app for testing
3. Check permissions and send test SMS

### Step 4: Test FCM Messages
Send a test FCM message with this payload:
```json
{
  "data": {
    "phoneNumber": "09123456789",
    "message": "Test SMS from FCM",
    "timestamp": "1640995200000",
    "signature": "your_hmac_signature_here"
  }
}
```

## Common Issues and Solutions

### Issue 1: SMS Permission Denied
**Symptoms**: Logs show "SMS permission not granted"
**Solution**: 
1. Check app permissions in device settings
2. Request permissions programmatically
3. Verify permission in AndroidManifest.xml

### Issue 2: FCM Messages Not Received
**Symptoms**: No FCM logs in logcat
**Solution**:
1. Verify Firebase configuration
2. Check internet connectivity
3. Verify FCM token registration

### Issue 3: Signature Verification Fails
**Symptoms**: "Invalid signature verification failed" in logs
**Solution**:
1. Check HMAC key configuration
2. Verify timestamp format
3. Check signature generation on server

### Issue 4: SMS Sending Fails
**Symptoms**: Platform channel errors or SMS not sent
**Solution**:
1. Check device SMS functionality
2. Verify phone number format
3. Check message length limits

## Testing Commands

### Send Test FCM Message (using curl)
```bash
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "YOUR_FCM_TOKEN",
    "data": {
      "phoneNumber": "09123456789",
      "message": "Test message",
      "timestamp": "'$(date +%s)'000",
      "signature": "test_signature"
    }
  }'
```

### Check App Processes
```bash
# Check if app is running
adb shell ps | grep message_divert

# Check background services
adb shell dumpsys activity services | grep message_divert
```

## Log Analysis

### Look for these log patterns:
1. **FCM Reception**: "FCM message received"
2. **Permission Check**: "SMS permission"
3. **SMS Sending**: "SMS sent successfully"
4. **Errors**: "ERROR", "Failed", "Exception"

### Key Log Tags:
- `FCMService`: FCM message processing
- `SmsManager`: SMS sending operations
- `SmsReceiver`: Incoming SMS handling
- `MainActivity`: App lifecycle and initialization
