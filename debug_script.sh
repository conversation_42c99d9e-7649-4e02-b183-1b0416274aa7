#!/bin/bash

echo "=== SMS Divert App Debug Script ==="
echo "This script will help you debug SMS sending issues"
echo ""

# Check if device is connected
echo "1. Checking device connection..."
adb devices
if [ $? -ne 0 ]; then
    echo "ERROR: ADB not found or device not connected"
    exit 1
fi

echo ""
echo "2. Checking app installation..."
adb shell pm list packages | grep message_divert
if [ $? -ne 0 ]; then
    echo "WARNING: App not installed or package name different"
fi

echo ""
echo "3. Checking app permissions..."
echo "SMS permissions:"
adb shell dumpsys package com.example.message_divert | grep -A 5 "android.permission.SEND_SMS"
echo ""
echo "Notification permissions:"
adb shell dumpsys package com.example.message_divert | grep -A 5 "android.permission.POST_NOTIFICATIONS"

echo ""
echo "4. Starting log monitoring..."
echo "Press Ctrl+C to stop monitoring"
echo "Now send a test FCM message or trigger SMS functionality"
echo ""

# Monitor logs with relevant filters
adb logcat -c  # Clear existing logs
adb logcat | grep -E "(FCMService|SmsManager|SMS|message_divert|FirebaseMessaging)" --color=always

echo ""
echo "Debug script completed."
echo ""
echo "=== Troubleshooting Tips ==="
echo "1. If no FCM logs appear:"
echo "   - Check Firebase configuration"
echo "   - Verify FCM token registration"
echo "   - Test with Firebase Console"
echo ""
echo "2. If SMS permission denied:"
echo "   - Go to Settings > Apps > Message Divert > Permissions"
echo "   - Enable SMS permissions manually"
echo ""
echo "3. If signature verification fails:"
echo "   - Use the test_fcm_sms.dart page to generate valid signatures"
echo "   - Check timestamp format (should be milliseconds)"
echo ""
echo "4. If SMS sending fails:"
echo "   - Test with debug_sms_test.dart page"
echo "   - Check device SMS functionality"
echo "   - Verify phone number format"
