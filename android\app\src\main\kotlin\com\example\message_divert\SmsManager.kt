package com.example.message_divert

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.telephony.SmsManager
import androidx.core.app.ActivityCompat
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result

class SmsManagerHandler(private val context: Context) : MethodCallHandler {
    
    companion object {
        const val CHANNEL = "sms_manager"
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "sendSms" -> {
                val phoneNumber = call.argument<String>("phoneNumber")
                val message = call.argument<String>("message")
                
                if (phoneNumber != null && message != null) {
                    sendSms(phoneNumber, message, result)
                } else {
                    result.error("INVALID_ARGUMENTS", "Phone number and message are required", null)
                }
            }
            "hasPermission" -> {
                result.success(hasPermission())
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun sendSms(phoneNumber: String, message: String, result: Result) {
        try {
            if (!hasPermission()) {
                result.error("NO_PERMISSION", "SMS permission not granted", null)
                return
            }

            val smsManager = SmsManager.getDefault()
            
            // Split message if it's too long
            val parts = smsManager.divideMessage(message)
            
            if (parts.size == 1) {
                smsManager.sendTextMessage(phoneNumber, null, message, null, null)
            } else {
                smsManager.sendMultipartTextMessage(phoneNumber, null, parts, null, null)
            }
            
            result.success(true)
        } catch (e: Exception) {
            result.error("SMS_SEND_FAILED", "Failed to send SMS: ${e.message}", null)
        }
    }

    private fun hasPermission(): Boolean {
        return ActivityCompat.checkSelfPermission(
            context,
            Manifest.permission.SEND_SMS
        ) == PackageManager.PERMISSION_GRANTED
    }
}
