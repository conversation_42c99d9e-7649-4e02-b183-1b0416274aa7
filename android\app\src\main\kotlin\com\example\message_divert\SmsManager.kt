package com.example.message_divert

import android.Manifest
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.telephony.SmsManager
import android.util.Log
import androidx.core.app.ActivityCompat
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result

class SmsManagerHandler(private val context: Context) : MethodCallHandler {

    companion object {
        const val CHANNEL = "sms_manager"
        const val SMS_SENT_ACTION = "SMS_SENT"
        const val SMS_DELIVERED_ACTION = "SMS_DELIVERED"
        private const val TAG = "SmsManagerHandler"
    }

    private var currentResult: Result? = null

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "sendSms" -> {
                val phoneNumber = call.argument<String>("phoneNumber")
                val message = call.argument<String>("message")
                
                if (phoneNumber != null && message != null) {
                    sendSms(phoneNumber, message, result)
                } else {
                    result.error("INVALID_ARGUMENTS", "Phone number and message are required", null)
                }
            }
            "hasPermission" -> {
                result.success(hasPermission())
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun sendSms(phoneNumber: String, message: String, result: Result) {
        try {
            if (!hasPermission()) {
                result.error("NO_PERMISSION", "SMS permission not granted", null)
                return
            }

            // Store the result to respond later when we get delivery confirmation
            currentResult = result

            // Register broadcast receivers for SMS status
            registerSmsReceivers()

            Log.d(TAG, "Attempting to send SMS to: $phoneNumber")
            Log.d(TAG, "Message: $message")

            val smsManager = SmsManager.getDefault()

            // Create pending intents for sent and delivered status
            val sentIntent = PendingIntent.getBroadcast(
                context, 0, Intent(SMS_SENT_ACTION),
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            val deliveredIntent = PendingIntent.getBroadcast(
                context, 0, Intent(SMS_DELIVERED_ACTION),
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // Split message if it's too long
            val parts = smsManager.divideMessage(message)

            if (parts.size == 1) {
                smsManager.sendTextMessage(phoneNumber, null, message, sentIntent, deliveredIntent)
                Log.d(TAG, "Single SMS queued for sending")
            } else {
                val sentIntents = arrayListOf(sentIntent)
                val deliveredIntents = arrayListOf(deliveredIntent)
                smsManager.sendMultipartTextMessage(phoneNumber, null, parts, sentIntents, deliveredIntents)
                Log.d(TAG, "Multipart SMS queued for sending (${parts.size} parts)")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Exception while sending SMS", e)
            result.error("SMS_SEND_FAILED", "Failed to send SMS: ${e.message}", null)
            currentResult = null
        }
    }

    private fun hasPermission(): Boolean {
        return ActivityCompat.checkSelfPermission(
            context,
            Manifest.permission.SEND_SMS
        ) == PackageManager.PERMISSION_GRANTED
    }

    private fun registerSmsReceivers() {
        // Register receiver for SMS sent status
        val sentReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (resultCode) {
                    android.app.Activity.RESULT_OK -> {
                        Log.d(TAG, "SMS sent successfully")
                        // Don't respond yet, wait for delivery confirmation
                    }
                    SmsManager.RESULT_ERROR_GENERIC_FAILURE -> {
                        Log.e(TAG, "SMS send failed: Generic failure")
                        currentResult?.error("SMS_SEND_FAILED", "Generic failure", null)
                        currentResult = null
                    }
                    SmsManager.RESULT_ERROR_NO_SERVICE -> {
                        Log.e(TAG, "SMS send failed: No service")
                        currentResult?.error("SMS_SEND_FAILED", "No service", null)
                        currentResult = null
                    }
                    SmsManager.RESULT_ERROR_NULL_PDU -> {
                        Log.e(TAG, "SMS send failed: Null PDU")
                        currentResult?.error("SMS_SEND_FAILED", "Null PDU", null)
                        currentResult = null
                    }
                    SmsManager.RESULT_ERROR_RADIO_OFF -> {
                        Log.e(TAG, "SMS send failed: Radio off")
                        currentResult?.error("SMS_SEND_FAILED", "Radio off", null)
                        currentResult = null
                    }
                    else -> {
                        Log.e(TAG, "SMS send failed: Unknown error code $resultCode")
                        currentResult?.error("SMS_SEND_FAILED", "Unknown error: $resultCode", null)
                        currentResult = null
                    }
                }
            }
        }

        // Register receiver for SMS delivery status
        val deliveredReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (resultCode) {
                    android.app.Activity.RESULT_OK -> {
                        Log.d(TAG, "SMS delivered successfully")
                        currentResult?.success(true)
                        currentResult = null
                    }
                    android.app.Activity.RESULT_CANCELED -> {
                        Log.e(TAG, "SMS delivery failed")
                        currentResult?.error("SMS_DELIVERY_FAILED", "SMS not delivered", null)
                        currentResult = null
                    }
                    else -> {
                        Log.e(TAG, "SMS delivery unknown status: $resultCode")
                        currentResult?.error("SMS_DELIVERY_UNKNOWN", "Unknown delivery status: $resultCode", null)
                        currentResult = null
                    }
                }
            }
        }

        // Register the receivers
        context.registerReceiver(sentReceiver, IntentFilter(SMS_SENT_ACTION))
        context.registerReceiver(deliveredReceiver, IntentFilter(SMS_DELIVERED_ACTION))
    }
}
