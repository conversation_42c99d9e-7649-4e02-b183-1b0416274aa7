{
  "to": "FIREBASE_TOKEN_HERE",
  "data": {
    "type": "special_sms",
    "phoneNumber": "09123456789",
    "message": "سلام! این یک پیام تست است.",
    "timestamp": "1692800000",
    "signature": "fd9601dcfb8490387135ec6727ec5690a9f7e35a54f85843ec9f0e418a829679"
  },
  "notification": {
    "title": "SMS Notification",
    "body": "New SMS to be sent"
  }
}

// Note: To generate a valid signature, use the TestUtils.generateValidFcmPayload() method
// or calculate HMAC-SHA256(secret="superSecretKey123", data="phoneNumber+message+timestamp")
//
// Example for the above payload:
// Data to sign: "09123456789" + "سلام! این یک پیام تست است." + "1692800000"
// Secret: "superSecretKey123"
// Signature: HMAC-SHA256(secret, data)
//
// Use the test_utils.dart file to generate valid payloads programmatically.
