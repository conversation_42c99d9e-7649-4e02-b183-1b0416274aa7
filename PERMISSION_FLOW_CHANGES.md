# Permission Flow Changes

## Overview
Modified the main app to hide the phone input interface until all necessary permissions are granted. The app now shows a permission request screen first, and only displays the phone number input after permissions are approved.

## Changes Made

### 1. Added Permission State Management
- `_permissionsGranted`: Tracks if all required permissions are granted
- `_checkingPermissions`: Shows loading state while checking permissions
- `_permissionStatuses`: Maps each permission to its current status

### 2. Modified App Flow
The app now has three distinct screens:

#### Permission Checking Screen
- Shows loading indicator while checking current permission status
- Displayed when `_checkingPermissions = true`

#### Permission Request Screen  
- Shows when permissions are not granted
- Lists all required permissions with their current status
- Color-coded status indicators (green=granted, red=denied, orange=unknown)
- "Request Permissions" button to request all permissions at once
- "Open App Settings" button for permanently denied permissions

#### Phone Input Screen
- Original phone number input interface
- Only shown when `_permissionsGranted = true`
- Same functionality as before

### 3. Required Permissions
The app checks for these permissions:
- **SMS Permission**: Required for sending SMS messages
- **Phone Permission**: Required for phone number validation
- **Notification Permission**: Required for FCM notifications

### 4. Permission Flow Logic

```
App Start
    ↓
Check Permissions
    ↓
All Granted? → YES → Show Phone Input
    ↓
    NO
    ↓
Show Permission Request Screen
    ↓
User Grants Permissions → Show Phone Input
    ↓
User Denies → Stay on Permission Screen
```

### 5. Key Methods Added

#### `_checkAndRequestPermissions()`
- Initial permission check on app start
- Updates permission status map
- Determines which screen to show

#### `_requestAllPermissions()`
- Requests all required permissions at once
- Updates UI based on results
- Initializes app services if all permissions granted

#### `_buildPermissionRequestScreen()`
- Creates the permission request UI
- Shows permission list with status indicators
- Handles permanently denied permissions

#### `_buildPermissionList()`
- Generates list of permission items
- Color-coded status indicators
- Persian text for permission names

#### `_buildPhoneInputScreen()`
- Original phone input interface
- Moved to separate method for better organization

## User Experience

### Before Changes
- User could see phone input immediately
- Permissions requested in background
- Confusing if permissions denied

### After Changes
- Clear permission request flow
- User understands what permissions are needed
- Cannot proceed without required permissions
- Better error handling for denied permissions

## Permission Status Indicators

| Status | Color | Icon | Text |
|--------|-------|------|------|
| Granted | Green | ✓ | مجاز |
| Denied | Red | ✗ | رد شده |
| Permanently Denied | Red | ⊘ | دائماً رد شده |
| Unknown | Orange | ? | نامشخص |

## Error Handling

### Permanently Denied Permissions
- Shows "Open App Settings" button
- Uses `openAppSettings()` to redirect to system settings
- User must manually enable permissions

### Partial Permission Grant
- App only proceeds when ALL permissions are granted
- Shows which specific permissions are missing
- User can retry permission request

## Testing the Changes

1. **Fresh Install**: App should show permission screen first
2. **Deny Permissions**: Should stay on permission screen
3. **Grant Permissions**: Should proceed to phone input
4. **Permanently Deny**: Should show settings button
5. **Partial Grant**: Should request remaining permissions

## Benefits

1. **Better UX**: Clear permission flow
2. **Security**: User understands what permissions are needed
3. **Compliance**: Follows Android permission best practices
4. **Error Prevention**: Prevents app malfunction due to missing permissions
5. **Accessibility**: Clear visual indicators for permission status
