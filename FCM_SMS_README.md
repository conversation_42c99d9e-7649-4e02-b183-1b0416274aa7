# FCM SMS Auto-Sender

This Flutter app automatically sends SMS messages when receiving specific Firebase Cloud Messaging (FCM) notifications with proper security verification.

## Features

- **FCM Integration**: Handles both foreground and background FCM messages
- **Security**: HMAC-SHA256 signature verification for all SMS requests
- **Iranian Phone Numbers**: Validates Iranian phone number format (09xxxxxxxxx)
- **Background Processing**: Works even when app is terminated
- **Permission Management**: Handles SMS permissions with user prompts
- **Error Handling**: Comprehensive error handling and user feedback
- **Logging**: SMS activity logging for debugging

## Security Model

### Signature Verification
All FCM payloads must include a valid HMAC-SHA256 signature:
- **Secret Key**: `superSecretKey123` (hardcoded)
- **Data to Sign**: `phoneNumber + message + timestamp`
- **Algorithm**: HMAC-SHA256
- **Format**: Hex string (lowercase)

### Payload Validation
- Type must be exactly `"special_sms"`
- Phone number must match Iranian format (09xxxxxxxxx)
- Timestamp must be within 5 minutes of current time
- Signature must match calculated HMAC

## FCM Payload Structure

### Required Data Fields
```json
{
  "to": "FIREBASE_TOKEN",
  "data": {
    "type": "special_sms",
    "phoneNumber": "09123456789",
    "message": "Your SMS message here",
    "timestamp": "1692800000",
    "signature": "calculated_hmac_sha256_signature"
  },
  "notification": {
    "title": "SMS Notification",
    "body": "New SMS to be sent"
  }
}
```

### Field Descriptions
- `type`: Must be "special_sms" to trigger SMS sending
- `phoneNumber`: Target phone number in Iranian format (09xxxxxxxxx)
- `message`: SMS content to send
- `timestamp`: Unix timestamp as string (for replay attack prevention)
- `signature`: HMAC-SHA256 signature for verification

## Usage

### 1. Setup
1. Install dependencies: `flutter pub get`
2. Configure Firebase project and add `google-services.json`
3. Build and install the app
4. Grant SMS permissions when prompted

### 2. Generate Valid Payloads
Use the `TestUtils` class to generate valid FCM payloads:

```dart
import 'lib/test_utils.dart';

// Generate a valid payload
final payload = TestUtils.generateValidFcmPayload(
  phoneNumber: '09123456789',
  message: 'سلام! این یک پیام تست است.',
);

// Generate complete FCM message
final fcmMessage = TestUtils.generateCompleteFcmMessage(
  firebaseToken: 'your_firebase_token',
  phoneNumber: '09123456789',
  message: 'سلام! این یک پیام تست است.',
);
```

### 3. Send FCM Message
Send the FCM message using Firebase Admin SDK or FCM HTTP API:

```bash
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d @example_fcm_payload.json
```

## File Structure

### Core Files
- `lib/main.dart`: Main app with UI and FCM initialization
- `lib/fcm_sms_service.dart`: FCM message processing and SMS sending
- `lib/crypto_utils.dart`: HMAC signature verification utilities
- `lib/test_utils.dart`: Testing utilities for generating valid payloads

### Configuration Files
- `android/app/src/main/AndroidManifest.xml`: Android permissions
- `pubspec.yaml`: Dependencies
- `example_fcm_payload.json`: Example FCM payload structure

## Permissions

### Android Permissions (AndroidManifest.xml)
```xml
<uses-permission android:name="android.permission.SEND_SMS" />
<uses-permission android:name="android.permission.RECEIVE_SMS" />
<uses-permission android:name="android.permission.READ_SMS" />
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
```

### Runtime Permissions
The app requests these permissions at runtime:
- SMS sending and receiving
- Phone state access
- Notification permissions

## Error Handling

### Common Error Scenarios
1. **Invalid Signature**: Signature verification fails
2. **Invalid Phone Number**: Not Iranian format (09xxxxxxxxx)
3. **Missing Permissions**: SMS permission denied
4. **Old Timestamp**: Message older than 5 minutes
5. **Malformed Payload**: Missing required fields

### Error Notifications
All errors are displayed to the user via snackbar notifications with detailed error messages.

## Logging

SMS activity is logged to SharedPreferences for debugging:
- Timestamp of SMS attempt
- Target phone number
- Message content
- Success/failure status

Access logs programmatically:
```dart
final fcmService = FcmSmsService();
final logs = await fcmService.getSmsLogs();
```

## Testing

### Generate Test Payloads
```dart
// Run test utility
flutter run lib/test_utils.dart

// Or use in code
TestUtils.printExamplePayloads();
```

### Test Invalid Payloads
```dart
final invalidPayload = TestUtils.generateInvalidFcmPayload(
  phoneNumber: '09123456789',
  message: 'Test message',
  invalidSignature: true,
);
```

## Security Considerations

1. **Secret Key**: Change the hardcoded secret key in production
2. **Timestamp Validation**: Prevents replay attacks
3. **Signature Verification**: Ensures message authenticity
4. **Permission Checks**: Validates SMS permissions before sending
5. **Input Validation**: Validates phone number format and payload structure

## Troubleshooting

### Common Issues
1. **FCM not working**: Check Firebase configuration and token
2. **SMS not sending**: Verify SMS permissions and phone number format
3. **Signature errors**: Ensure correct secret key and data concatenation
4. **Background not working**: Check background service permissions

### Debug Information
- Check console logs for detailed error messages
- Use SMS logs to track sending attempts
- Verify FCM payload structure matches requirements
