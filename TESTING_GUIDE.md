# FCM SMS Auto-Sender Testing Guide

## 🚀 Quick Start Testing

### 1. Install the App
```bash
# Install the APK on your Android device
adb install build/app/outputs/flutter-apk/app-arm64-v8a-release.apk
```

### 2. Setup Firebase Token
1. Launch the app
2. Enter a valid Iranian phone number (09xxxxxxxxx)
3. Grant SMS permissions when prompted
4. Copy the Firebase token from console logs or UI

### 3. Generate Valid Test Payload
```bash
# Run the signature calculator
dart calculate_signature.dart
```

This will output a complete FCM message with valid signature.

## 📱 Testing Scenarios

### Scenario 1: Valid FCM Message
**Expected Result**: SMS should be sent successfully

**Test Payload**:
```json
{
  "to": "YOUR_FIREBASE_TOKEN",
  "data": {
    "type": "special_sms",
    "phoneNumber": "09123456789",
    "message": "سلام! این یک پیام تست است.",
    "timestamp": "1692800000",
    "signature": "fd9601dcfb8490387135ec6727ec5690a9f7e35a54f85843ec9f0e418a829679"
  },
  "notification": {
    "title": "SMS Notification",
    "body": "New SMS to be sent"
  }
}
```

**Send via cURL**:
```bash
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d @test_payload.json
```

### Scenario 2: Invalid Signature
**Expected Result**: Error message "Invalid signature verification failed"

**Test**: Change the signature to an invalid value like "invalid_signature"

### Scenario 3: Wrong Type
**Expected Result**: Message ignored (no processing)

**Test**: Change `"type": "special_sms"` to `"type": "normal_message"`

### Scenario 4: Invalid Phone Number
**Expected Result**: Error message "Invalid Iranian phone number format"

**Test Cases**:
- `"phoneNumber": "08123456789"` (doesn't start with 09)
- `"phoneNumber": "091234567890"` (too long)
- `"phoneNumber": "0912345678"` (too short)

### Scenario 5: Missing Fields
**Expected Result**: Error message "Invalid FCM payload: missing required fields"

**Test**: Remove any required field (phoneNumber, message, timestamp, signature)

### Scenario 6: Old Timestamp
**Expected Result**: Error message "Timestamp validation failed - message too old"

**Test**: Use a timestamp older than 5 minutes from current time

## 🔧 Testing Tools

### 1. Signature Calculator
```bash
dart calculate_signature.dart
```
Generates valid signatures for any phone number and message combination.

### 2. Test Utils (Programmatic)
```dart
import 'lib/test_utils.dart';

// Generate valid payload
final payload = TestUtils.generateValidFcmPayload(
  phoneNumber: '09123456789',
  message: 'Test message',
);

// Generate invalid payload
final invalidPayload = TestUtils.generateInvalidFcmPayload(
  phoneNumber: '09123456789',
  message: 'Test message',
  invalidSignature: true,
);
```

### 3. FCM Testing via Firebase Console
1. Go to Firebase Console > Cloud Messaging
2. Create a new message
3. Add custom data fields as shown in test scenarios
4. Send to your device token

## 📊 Verification Steps

### 1. Check App Logs
Monitor console output for:
- FCM message received logs
- Signature verification results
- SMS sending status
- Error messages

### 2. Check SMS Delivery
- Verify SMS is received on target phone number
- Check message content matches FCM payload
- Confirm sender is your device

### 3. Check UI Feedback
- Success: Green snackbar with "SMS sent successfully"
- Error: Red snackbar with specific error message

### 4. Check SMS Logs
```dart
final fcmService = FcmSmsService();
final logs = await fcmService.getSmsLogs();
print('SMS Logs: $logs');
```

## 🐛 Troubleshooting

### Common Issues

#### 1. FCM Message Not Received
**Symptoms**: No logs, no UI feedback
**Solutions**:
- Check Firebase token is correct
- Verify Firebase project configuration
- Check internet connectivity
- Ensure app is running or in background

#### 2. Signature Verification Fails
**Symptoms**: "Invalid signature verification failed" error
**Solutions**:
- Verify secret key matches: "superSecretKey123"
- Check data concatenation: phoneNumber + message + timestamp
- Ensure signature is lowercase hex string
- Use signature calculator to generate correct signature

#### 3. SMS Permission Denied
**Symptoms**: "SMS permission denied" error
**Solutions**:
- Grant SMS permissions in app settings
- Restart app after granting permissions
- Check Android version compatibility

#### 4. SMS Not Sent
**Symptoms**: Permission granted but SMS fails
**Solutions**:
- Check phone number format (09xxxxxxxxx)
- Verify device has SMS capability
- Check network connectivity
- Try with different phone number

#### 5. Background Processing Not Working
**Symptoms**: Works in foreground but not background
**Solutions**:
- Check background app restrictions
- Verify foreground service permissions
- Test with app completely closed

## 📋 Test Checklist

### Pre-Testing Setup
- [ ] App installed on Android device
- [ ] Firebase project configured
- [ ] SMS permissions granted
- [ ] Valid phone number entered
- [ ] Firebase token obtained

### Functional Tests
- [ ] Valid FCM message sends SMS successfully
- [ ] Invalid signature rejected
- [ ] Wrong message type ignored
- [ ] Invalid phone number rejected
- [ ] Missing fields rejected
- [ ] Old timestamp rejected
- [ ] Permission denied handled gracefully

### Background Tests
- [ ] FCM works when app in background
- [ ] FCM works when app terminated
- [ ] Background service continues running
- [ ] SMS sent from background state

### Error Handling Tests
- [ ] Network errors handled
- [ ] Malformed JSON handled
- [ ] SMS sending failures logged
- [ ] User feedback provided for all errors

### Security Tests
- [ ] Only "special_sms" type processed
- [ ] Signature verification enforced
- [ ] Timestamp validation prevents replay
- [ ] Invalid payloads rejected safely

## 🎯 Success Criteria

✅ **Test Passes If**:
1. Valid FCM message triggers SMS sending
2. SMS is delivered to correct phone number
3. Invalid signatures are rejected
4. Error messages are clear and helpful
5. Background processing works reliably
6. All security validations function correctly

❌ **Test Fails If**:
1. Valid messages don't send SMS
2. Invalid signatures are accepted
3. SMS sent to wrong number
4. App crashes on invalid input
5. Background processing stops working
6. Security validations can be bypassed

## 📞 Support

For testing issues:
1. Check console logs for detailed error messages
2. Verify FCM payload structure matches requirements
3. Use provided testing tools to generate valid payloads
4. Ensure all permissions are granted
5. Test with different phone numbers and messages
