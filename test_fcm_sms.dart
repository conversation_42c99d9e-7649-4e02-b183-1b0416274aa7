import 'package:flutter/material.dart';
import 'lib/crypto_utils.dart';

class FcmSmsTestPage extends StatefulWidget {
  const FcmSmsTestPage({super.key});

  @override
  State<FcmSmsTestPage> createState() => _FcmSmsTestPageState();
}

class _FcmSmsTestPageState extends State<FcmSmsTestPage> {
  final TextEditingController _phoneController = TextEditingController(text: '09123456789');
  final TextEditingController _messageController = TextEditingController(text: 'Test message');
  final TextEditingController _timestampController = TextEditingController();
  String _generatedSignature = '';
  String _testResults = '';

  @override
  void initState() {
    super.initState();
    _timestampController.text = DateTime.now().millisecondsSinceEpoch.toString();
  }

  void _generateSignature() {
    final signature = CryptoUtils.generateSignature(
      phoneNumber: _phoneController.text,
      message: _messageController.text,
      timestamp: _timestampController.text,
    );
    
    setState(() {
      _generatedSignature = signature;
    });
  }

  void _testSignatureVerification() {
    final isValid = CryptoUtils.verifySignature(
      phoneNumber: _phoneController.text,
      message: _messageController.text,
      timestamp: _timestampController.text,
      signature: _generatedSignature,
    );
    
    setState(() {
      _testResults = 'Signature verification: ${isValid ? 'PASSED' : 'FAILED'}\n';
      _testResults += 'Phone: ${_phoneController.text}\n';
      _testResults += 'Message: ${_messageController.text}\n';
      _testResults += 'Timestamp: ${_timestampController.text}\n';
      _testResults += 'Signature: $_generatedSignature\n';
      _testResults += 'Phone number valid: ${CryptoUtils.isValidIranianPhoneNumber(_phoneController.text)}\n';
    });
  }

  void _generateFcmPayload() {
    _generateSignature();
    
    final payload = '''
{
  "data": {
    "phoneNumber": "${_phoneController.text}",
    "message": "${_messageController.text}",
    "timestamp": "${_timestampController.text}",
    "signature": "$_generatedSignature"
  }
}''';

    setState(() {
      _testResults = 'FCM Test Payload:\n$payload\n\n';
      _testResults += 'Use this payload to test FCM message sending.\n';
      _testResults += 'Send it to your FCM token using Firebase Console or API.\n';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('FCM SMS Test'),
        backgroundColor: Colors.green,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            TextField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone Number',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _messageController,
              decoration: const InputDecoration(
                labelText: 'Message',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _timestampController,
              decoration: const InputDecoration(
                labelText: 'Timestamp',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            if (_generatedSignature.isNotEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Generated Signature:', style: TextStyle(fontWeight: FontWeight.bold)),
                    Text(_generatedSignature, style: const TextStyle(fontFamily: 'monospace')),
                  ],
                ),
              ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _generateSignature,
                    child: const Text('Generate Signature'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _testSignatureVerification,
                    child: const Text('Test Verification'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _generateFcmPayload,
                style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                child: const Text('Generate FCM Test Payload'),
              ),
            ),
            const SizedBox(height: 16),
            if (_testResults.isNotEmpty)
              Expanded(
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: SingleChildScrollView(
                    child: Text(
                      _testResults,
                      style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
