import 'crypto_utils.dart';

/// Test utilities for generating valid FCM payloads
class TestUtils {
  
  /// Generate a valid FCM payload for testing
  /// 
  /// This creates a complete FCM data payload with valid signature
  static Map<String, dynamic> generateValidFcmPayload({
    required String phoneNumber,
    required String message,
    String? customTimestamp,
  }) {
    // Use current timestamp if not provided
    final timestamp = customTimestamp ?? 
        (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString();
    
    // Generate valid signature
    final signature = CryptoUtils.generateSignature(
      phoneNumber: phoneNumber,
      message: message,
      timestamp: timestamp,
    );
    
    return {
      'type': 'special_sms',
      'phoneNumber': phoneNumber,
      'message': message,
      'timestamp': timestamp,
      'signature': signature,
    };
  }
  
  /// Generate a complete FCM message for testing
  static Map<String, dynamic> generateCompleteFcmMessage({
    required String firebaseToken,
    required String phoneNumber,
    required String message,
    String? customTimestamp,
  }) {
    final data = generateValidFcmPayload(
      phoneNumber: phoneNumber,
      message: message,
      customTimestamp: customTimestamp,
    );
    
    return {
      'to': firebaseToken,
      'data': data,
      'notification': {
        'title': 'SMS Notification',
        'body': 'New SMS to be sent to $phoneNumber',
      },
    };
  }
  
  /// Generate an invalid FCM payload for testing error handling
  static Map<String, dynamic> generateInvalidFcmPayload({
    required String phoneNumber,
    required String message,
    String? customTimestamp,
    bool invalidSignature = true,
    bool invalidType = false,
    bool missingFields = false,
  }) {
    final timestamp = customTimestamp ?? 
        (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString();
    
    String signature;
    if (invalidSignature) {
      signature = 'invalid_signature_12345';
    } else {
      signature = CryptoUtils.generateSignature(
        phoneNumber: phoneNumber,
        message: message,
        timestamp: timestamp,
      );
    }
    
    final data = <String, dynamic>{};
    
    if (!missingFields) {
      data['type'] = invalidType ? 'invalid_type' : 'special_sms';
      data['phoneNumber'] = phoneNumber;
      data['message'] = message;
      data['timestamp'] = timestamp;
      data['signature'] = signature;
    } else {
      // Missing some required fields
      data['type'] = 'special_sms';
      data['phoneNumber'] = phoneNumber;
      // Missing message, timestamp, and signature
    }
    
    return data;
  }
  
  /// Print example payloads for documentation
  static void printExamplePayloads() {
    print('=== VALID FCM PAYLOAD EXAMPLE ===');
    final validPayload = generateValidFcmPayload(
      phoneNumber: '09123456789',
      message: 'سلام! این یک پیام تست است.',
    );
    print('Data payload:');
    validPayload.forEach((key, value) {
      print('  "$key": "$value"');
    });
    
    print('\n=== COMPLETE FCM MESSAGE EXAMPLE ===');
    final completeMessage = generateCompleteFcmMessage(
      firebaseToken: 'FIREBASE_TOKEN_HERE',
      phoneNumber: '09123456789',
      message: 'سلام! این یک پیام تست است.',
    );
    print('Complete FCM message:');
    print(completeMessage);
    
    print('\n=== INVALID PAYLOAD EXAMPLE ===');
    final invalidPayload = generateInvalidFcmPayload(
      phoneNumber: '09123456789',
      message: 'سلام! این یک پیام تست است.',
      invalidSignature: true,
    );
    print('Invalid payload (bad signature):');
    invalidPayload.forEach((key, value) {
      print('  "$key": "$value"');
    });
  }
}

/// Example usage and testing
void main() {
  TestUtils.printExamplePayloads();
}
