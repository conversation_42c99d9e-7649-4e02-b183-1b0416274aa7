import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';

class ComprehensiveSmsDebugPage extends StatefulWidget {
  const ComprehensiveSmsDebugPage({super.key});

  @override
  State<ComprehensiveSmsDebugPage> createState() => _ComprehensiveSmsDebugPageState();
}

class _ComprehensiveSmsDebugPageState extends State<ComprehensiveSmsDebugPage> {
  static const MethodChannel _smsChannel = MethodChannel('sms_manager');
  final TextEditingController _phoneController = TextEditingController(text: '09046351997');
  final TextEditingController _messageController = TextEditingController(text: 'Test SMS from debug tool');
  String _debugLog = '';
  bool _isTesting = false;

  @override
  void initState() {
    super.initState();
    _addToLog('SMS Debug Tool Initialized');
    _performInitialChecks();
  }

  void _addToLog(String message) {
    final timestamp = DateTime.now().toString().substring(11, 19);
    setState(() {
      _debugLog += '[$timestamp] $message\n';
    });
    print('SMS Debug: $message');
  }

  Future<void> _performInitialChecks() async {
    _addToLog('=== INITIAL SYSTEM CHECKS ===');
    
    // Check device info
    _addToLog('Device: Android');
    
    // Check permissions
    await _checkAllPermissions();
    
    // Check SMS capability
    await _checkSmsCapability();
    
    _addToLog('=== INITIAL CHECKS COMPLETE ===');
  }

  Future<void> _checkAllPermissions() async {
    _addToLog('--- Permission Checks ---');
    
    // Check SMS permission via permission_handler
    final smsStatus = await Permission.sms.status;
    _addToLog('SMS Permission (permission_handler): $smsStatus');
    
    // Check SMS permission via platform channel
    try {
      final hasNativePermission = await _smsChannel.invokeMethod('hasPermission');
      _addToLog('SMS Permission (native): $hasNativePermission');
    } catch (e) {
      _addToLog('ERROR checking native permission: $e');
    }
    
    // Check phone permission
    final phoneStatus = await Permission.phone.status;
    _addToLog('Phone Permission: $phoneStatus');
    
    // Check notification permission
    final notificationStatus = await Permission.notification.status;
    _addToLog('Notification Permission: $notificationStatus');
  }

  Future<void> _checkSmsCapability() async {
    _addToLog('--- SMS Capability Checks ---');
    
    try {
      // Try to invoke SMS channel methods
      final hasPermission = await _smsChannel.invokeMethod('hasPermission');
      _addToLog('SMS Channel accessible: true');
      _addToLog('SMS Channel permission result: $hasPermission');
    } catch (e) {
      _addToLog('ERROR: SMS Channel not accessible: $e');
    }
  }

  Future<void> _testSmsStep1_Permissions() async {
    _addToLog('=== STEP 1: PERMISSION VERIFICATION ===');
    
    // Re-check permissions
    await _checkAllPermissions();
    
    // Request permissions if needed
    final smsStatus = await Permission.sms.status;
    if (!smsStatus.isGranted) {
      _addToLog('Requesting SMS permission...');
      final result = await Permission.sms.request();
      _addToLog('Permission request result: $result');
    }
  }

  Future<void> _testSmsStep2_PlatformChannel() async {
    _addToLog('=== STEP 2: PLATFORM CHANNEL TEST ===');
    
    try {
      final hasPermission = await _smsChannel.invokeMethod('hasPermission');
      _addToLog('Platform channel permission check: $hasPermission');
      
      if (hasPermission != true) {
        _addToLog('ERROR: Platform channel reports no SMS permission');
        return;
      }
      
      _addToLog('Platform channel is working correctly');
    } catch (e) {
      _addToLog('ERROR: Platform channel failed: $e');
    }
  }

  Future<void> _testSmsStep3_ActualSend() async {
    _addToLog('=== STEP 3: ACTUAL SMS SEND TEST ===');
    
    if (_phoneController.text.isEmpty || _messageController.text.isEmpty) {
      _addToLog('ERROR: Phone number and message are required');
      return;
    }

    _addToLog('Target phone: ${_phoneController.text}');
    _addToLog('Message: ${_messageController.text}');
    _addToLog('Message length: ${_messageController.text.length} characters');

    try {
      _addToLog('Calling SMS send method...');
      
      final result = await _smsChannel.invokeMethod('sendSms', {
        'phoneNumber': _phoneController.text,
        'message': _messageController.text,
      });

      _addToLog('SMS send method returned: $result');
      
      if (result == true) {
        _addToLog('SUCCESS: SMS send method completed successfully');
        _addToLog('NOTE: This means SMS was queued, not necessarily delivered');
        _addToLog('Check your device SMS app for actual delivery status');
      } else {
        _addToLog('WARNING: SMS send method returned false or null');
      }

    } catch (e) {
      _addToLog('ERROR: Exception during SMS send: $e');
      _addToLog('Error type: ${e.runtimeType}');
    }
  }

  Future<void> _runFullTest() async {
    if (_isTesting) return;
    
    setState(() {
      _isTesting = true;
      _debugLog = '';
    });

    _addToLog('=== COMPREHENSIVE SMS TEST STARTED ===');
    
    await _testSmsStep1_Permissions();
    await Future.delayed(const Duration(seconds: 1));
    
    await _testSmsStep2_PlatformChannel();
    await Future.delayed(const Duration(seconds: 1));
    
    await _testSmsStep3_ActualSend();
    
    _addToLog('=== COMPREHENSIVE SMS TEST COMPLETED ===');
    _addToLog('');
    _addToLog('TROUBLESHOOTING TIPS:');
    _addToLog('1. Check device SMS app for sent messages');
    _addToLog('2. Try sending to a different number');
    _addToLog('3. Check carrier SMS settings');
    _addToLog('4. Verify phone number format');
    _addToLog('5. Check device storage space');
    _addToLog('6. Try shorter message text');
    
    setState(() {
      _isTesting = false;
    });
  }

  void _clearLog() {
    setState(() {
      _debugLog = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Comprehensive SMS Debug'),
        backgroundColor: Colors.red,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            TextField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone Number',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _messageController,
              decoration: const InputDecoration(
                labelText: 'Message',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isTesting ? null : _runFullTest,
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                    child: _isTesting 
                        ? const CircularProgressIndicator(color: Colors.white)
                        : const Text('Run Full Test', style: TextStyle(color: Colors.white)),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _clearLog,
                  child: const Text('Clear'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Debug Log:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _debugLog.isEmpty ? 'No logs yet...' : _debugLog,
                    style: const TextStyle(fontFamily: 'monospace', fontSize: 11),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
